const fs = require("fs").promises;
const path = require("path");
const { google } = require("googleapis");
const http = require("http");
const url = require("url");
const { exec } = require("child_process");
const { promisify } = require("util");
const net = require("net");

const execAsync = promisify(exec);

class TokenManager {
  constructor(credentials) {
    this.tokenPath = path.join(__dirname, "token.json");
    this.credentials = credentials;
    this.callbackPort = null; // Will be set dynamically

    // Initial OAuth client setup (will be updated with correct redirect URI)
    this.oauth2Client = new google.auth.OAuth2(
      credentials.GOOGLE_CLIENT_ID,
      credentials.GOOGLE_CLIENT_SECRET,
      credentials.GOOGLE_REDIRECT_URI || "http://localhost:3001/oauth/callback" // Fallback, will be updated
    );

    // Scopes c<PERSON>n thiết
    this.SCOPES = [
      "https://www.googleapis.com/auth/documents.readonly",
      "https://www.googleapis.com/auth/drive.readonly",
    ];

    console.log("🔧 TokenManager initialized");
  }

  /**
   * Tìm port khả dụng bắt đầu từ startPort
   */
  async findAvailablePort(startPort = 3001, maxPort = 3100) {
    for (let port = startPort; port <= maxPort; port++) {
      if (await this.isPortAvailable(port)) {
        return port;
      }
    }
    throw new Error(
      `No available port found between ${startPort} and ${maxPort}`
    );
  }

  /**
   * Kiểm tra port có khả dụng không
   */
  async isPortAvailable(port) {
    return new Promise((resolve) => {
      const server = net.createServer();

      server.listen(port, () => {
        server.once("close", () => {
          resolve(true);
        });
        server.close();
      });

      server.on("error", () => {
        resolve(false);
      });
    });
  }

  /**
   * Lấy authenticated client, tự động handle token lifecycle với retry
   */
  async getAuthenticatedClient(retryCount = 0) {
    const maxRetries = 2;

    try {
      // 1. Thử load token từ file
      const tokenData = await this.loadTokenFromFile();

      if (tokenData) {
        console.log("📄 Found existing token file");
        this.oauth2Client.setCredentials(tokenData);

        // 2. Kiểm tra token có hết hạn không
        if (await this.isTokenValid(tokenData)) {
          console.log("✅ Token is still valid");
          return this.oauth2Client;
        } else {
          console.log("⏰ Token expired, refreshing...");
          return await this.refreshToken();
        }
      } else {
        console.log("❌ No token found, starting authorization flow...");
        return await this.startAuthorizationFlow();
      }
    } catch (error) {
      console.error("❌ Error in getAuthenticatedClient:", error.message);

      // Các trường hợp cần retry hoặc re-authorize
      const shouldRetry =
        error.message.includes("invalid_grant") ||
        error.message.includes("ENOTFOUND") ||
        error.message.includes("ECONNRESET") ||
        error.message.includes("timeout") ||
        error.message.includes("Invalid token structure") ||
        error.message.includes("Unexpected end of JSON input");

      if (shouldRetry) {
        if (retryCount < maxRetries) {
          console.log(`🔄 Retrying... (${retryCount + 1}/${maxRetries})`);

          // Remove potentially corrupted token
          await this.removeTokenFile();

          // Wait a bit before retry
          await new Promise((resolve) => setTimeout(resolve, 1000));

          return await this.getAuthenticatedClient(retryCount + 1);
        } else {
          console.log("🗑️ Max retries reached, forcing fresh authorization...");
          await this.removeTokenFile();
          return await this.startAuthorizationFlow();
        }
      }

      throw error;
    }
  }

  /**
   * Load token từ file token.json
   */
  async loadTokenFromFile() {
    try {
      const tokenData = await fs.readFile(this.tokenPath, "utf8");

      // Kiểm tra file có trống không
      if (!tokenData.trim()) {
        console.log("🗑️ Token file is empty, removing...");
        await this.removeTokenFile();
        return null;
      }

      // Thử parse JSON
      const parsed = JSON.parse(tokenData);

      // Validate token structure
      if (!parsed || typeof parsed !== "object") {
        console.log("🗑️ Invalid token structure, removing...");
        await this.removeTokenFile();
        return null;
      }

      return parsed;
    } catch (error) {
      if (error.code === "ENOENT") {
        return null; // File không tồn tại
      }

      // Nếu lỗi JSON parse hoặc bất kỳ lỗi nào khác
      console.error("❌ Error loading token file:", error.message);
      console.log("🗑️ Corrupted token file detected, removing...");

      try {
        await this.removeTokenFile();
      } catch (removeError) {
        console.error(
          "❌ Failed to remove corrupted token file:",
          removeError.message
        );
      }

      return null; // Return null để trigger re-authorization
    }
  }

  /**
   * Lưu token vào file token.json với atomic write
   */
  async saveTokenToFile(tokenData) {
    try {
      const tokenWithMetadata = {
        ...tokenData,
        created_at: new Date().toISOString(),
        expires_at: tokenData.expiry_date
          ? new Date(tokenData.expiry_date).toISOString()
          : null,
      };

      const tokenJson = JSON.stringify(tokenWithMetadata, null, 2);
      const tempPath = this.tokenPath + ".tmp";

      // Atomic write: write to temp file first, then rename
      await fs.writeFile(tempPath, tokenJson, "utf8");

      // Verify the written file can be parsed
      const verification = await fs.readFile(tempPath, "utf8");
      JSON.parse(verification); // Will throw if invalid

      // Atomic rename
      await fs.rename(tempPath, this.tokenPath);

      console.log("💾 Token saved to token.json");
    } catch (error) {
      console.error("❌ Failed to save token:", error.message);

      // Cleanup temp file if exists
      try {
        await fs.unlink(this.tokenPath + ".tmp");
      } catch (cleanupError) {
        // Ignore cleanup errors
      }

      throw new Error(`Failed to save token: ${error.message}`);
    }
  }

  /**
   * Xóa file token
   */
  async removeTokenFile() {
    try {
      await fs.unlink(this.tokenPath);
      console.log("🗑️ Token file removed");
    } catch (error) {
      if (error.code !== "ENOENT") {
        console.error("❌ Error removing token file:", error.message);
      }
    }
  }

  /**
   * Kiểm tra token có còn valid không
   */
  async isTokenValid(tokenData) {
    if (!tokenData.expiry_date) {
      return false;
    }

    // Kiểm tra còn ít nhất 5 phút trước khi hết hạn
    const expiryTime = new Date(tokenData.expiry_date).getTime();
    const currentTime = Date.now();
    const timeLeft = expiryTime - currentTime;

    // Còn ít nhất 5 phút (300000ms)
    return timeLeft > 300000;
  }

  /**
   * Refresh access token sử dụng refresh token
   */
  async refreshToken() {
    try {
      const { credentials } = await this.oauth2Client.refreshAccessToken();
      this.oauth2Client.setCredentials(credentials);

      // Lưu token mới vào file
      await this.saveTokenToFile(credentials);

      console.log("✅ Token refreshed successfully");
      return this.oauth2Client;
    } catch (error) {
      console.error("❌ Failed to refresh token:", error.message);
      throw error;
    }
  }

  /**
   * Bắt đầu authorization flow hoàn toàn tự động với dynamic port
   */
  async startAuthorizationFlow() {
    console.log("🚀 Starting automatic authorization flow...");

    try {
      // 1. Tìm port khả dụng cho callback server
      this.callbackPort = await this.findAvailablePort(3001, 3100);
      console.log(`📡 Using callback port: ${this.callbackPort}`);

      // 2. Cập nhật OAuth client với redirect URI đúng
      const redirectUri = `http://localhost:${this.callbackPort}/oauth/callback`;
      this.oauth2Client = new google.auth.OAuth2(
        this.credentials.GOOGLE_CLIENT_ID,
        this.credentials.GOOGLE_CLIENT_SECRET,
        redirectUri
      );

      // 3. Tạo authorization URL
      const authUrl = this.oauth2Client.generateAuthUrl({
        access_type: "offline",
        scope: this.SCOPES,
        prompt: "consent", // Force consent để lấy refresh token
      });

      console.log("🌐 Opening browser for authorization...");

      // 4. Khởi động temporary server để nhận callback
      const authCodePromise = this.startCallbackServer();

      // 5. Mở browser tự động
      await this.openBrowser(authUrl);

      // 6. Chờ authorization code
      console.log("⏳ Waiting for authorization...");
      const code = await authCodePromise;

      // 7. Exchange code lấy tokens
      console.log("🔄 Exchanging authorization code for tokens...");
      const { tokens } = await this.oauth2Client.getToken(code);

      // 8. Lưu tokens
      this.oauth2Client.setCredentials(tokens);
      await this.saveTokenToFile(tokens);

      console.log("🎉 Authorization completed successfully!");
      return this.oauth2Client;
    } catch (error) {
      console.error("❌ Authorization flow failed:", error.message);
      throw error;
    }
  }

  /**
   * Tạo temporary HTTP server để nhận OAuth callback
   */
  async startCallbackServer() {
    return new Promise((resolve, reject) => {
      const server = http.createServer((req, res) => {
        const queryObject = url.parse(req.url, true).query;

        if (queryObject.code) {
          // Success response
          res.writeHead(200, { "Content-Type": "text/html" });
          res.end(`
            <html>
              <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                <h1 style="color: green;">✅ Authorization Successful!</h1>
                <p>Token has been saved successfully!</p>
                <p>You can close this tab and return to your application.</p>
                <script>
                  setTimeout(() => window.close(), 3000);
                </script>
              </body>
            </html>
          `);

          server.close();
          resolve(queryObject.code);
        } else if (queryObject.error) {
          // Error response
          res.writeHead(400, { "Content-Type": "text/html" });
          res.end(`
            <html>
              <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                <h1 style="color: red;">❌ Authorization Failed</h1>
                <p>Error: ${queryObject.error}</p>
                <p>Please try again.</p>
              </body>
            </html>
          `);

          server.close();
          reject(new Error(`Authorization failed: ${queryObject.error}`));
        }
      });

      // Handle server errors (port conflicts, etc.)
      server.on("error", (error) => {
        console.error(`❌ Callback server error:`, error.message);
        reject(new Error(`Callback server failed: ${error.message}`));
      });

      // Lắng nghe trên port đã được tìm thấy
      server.listen(this.callbackPort, () => {
        console.log(
          `🔗 Callback server started on http://localhost:${this.callbackPort}`
        );
      });

      // Timeout sau 5 phút
      setTimeout(() => {
        server.close();
        reject(new Error("Authorization timeout"));
      }, 5 * 60 * 1000);
    });
  }

  /**
   * Mở browser tự động
   */
  async openBrowser(url) {
    try {
      const platform = process.platform;
      let command;

      switch (platform) {
        case "win32":
          command = `start "" "${url}"`;
          break;
        case "darwin":
          command = `open "${url}"`;
          break;
        case "linux":
          command = `xdg-open "${url}"`;
          break;
        default:
          console.log("❌ Cannot auto-open browser. Please manually open:");
          console.log(url);
          return;
      }

      await execAsync(command);
      console.log("✅ Browser opened successfully");
    } catch (error) {
      console.log(
        "❌ Failed to open browser automatically. Please manually open:"
      );
      console.log(url);
    }
  }

  /**
   * Get token info for debugging
   */
  getTokenInfo() {
    const credentials = this.oauth2Client.credentials;
    return {
      hasAccessToken: !!credentials.access_token,
      hasRefreshToken: !!credentials.refresh_token,
      expiryDate: credentials.expiry_date,
      isExpired: credentials.expiry_date
        ? credentials.expiry_date < Date.now()
        : null,
      timeToExpiry: credentials.expiry_date
        ? Math.max(0, credentials.expiry_date - Date.now())
        : null,
      timeToExpiryHuman: credentials.expiry_date
        ? `${Math.round(
            (credentials.expiry_date - Date.now()) / 1000 / 60
          )} minutes`
        : null,
    };
  }

  /**
   * Test connection với Google APIs
   */
  async testConnection() {
    try {
      const authClient = await this.getAuthenticatedClient();
      const drive = google.drive({ version: "v3", auth: authClient });

      const response = await drive.about.get({ fields: "user" });

      return {
        success: true,
        user: response.data.user,
        tokenInfo: this.getTokenInfo(),
        message: "Connection successful",
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: "Connection failed",
      };
    }
  }
}

module.exports = TokenManager;
