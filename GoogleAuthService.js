const { google } = require("googleapis");
const TokenManager = require("./TokenManager");
require("dotenv").config();

class GoogleAuthService {
  constructor() {
    // 1. <PERSON><PERSON><PERSON> tra credentials cơ bản
    const { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET } = process.env;

    if (!GOOGLE_CLIENT_ID || !GOOGLE_CLIENT_SECRET) {
      throw new Error(
        "Google credentials not configured. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in environment variables."
      );
    }

    // 2. Khởi tạo TokenManager để tự động quản lý tokens
    this.tokenManager = new TokenManager({
      GOOGLE_CLIENT_ID,
      GOOGLE_CLIENT_SECRET,
      GOOGLE_REDIRECT_URI: process.env.GOOGLE_REDIRECT_URI,
    });

    console.log(
      "✅ GoogleAuthService initialized with automatic token management."
    );
  }

  /**
   * <PERSON><PERSON><PERSON> ch<PERSON>h để lấy một client đã được xác thực.
   * TokenManager sẽ tự động handle toàn bộ token lifecycle.
   */
  async getAuthClient() {
    return await this.tokenManager.getAuthenticatedClient();
  }

  /**
   * Lấy access token hiện tại
   */
  async getAccessToken() {
    const authClient = await this.getAuthClient();
    const { token } = await authClient.getAccessToken();
    if (!token) throw new Error("Failed to obtain Google access token.");
    return token;
  }

  /**
   * Kiểm tra kết nối và credentials
   */
  async testConnection() {
    return await this.tokenManager.testConnection();
  }

  /**
   * Lấy thông tin về token hiện tại
   */
  getTokenInfo() {
    return this.tokenManager.getTokenInfo();
  }
}

module.exports = GoogleAuthService;
