const GoogleAuthService = require("./GoogleAuthService");
require("dotenv").config();

console.log("🧪 Testing Automatic Token Management\n");

async function testTokenManagement() {
  try {
    console.log("1️⃣ Initializing GoogleAuthService...");
    const authService = new GoogleAuthService();

    console.log("\n2️⃣ Testing connection (will auto-authorize if needed)...");
    const result = await authService.testConnection();

    if (result.success) {
      console.log("✅ Connection successful!");
      console.log(
        `👤 Authenticated as: ${result.user.displayName} (${result.user.emailAddress})`
      );

      console.log("\n📊 Token Info:");
      console.log(JSON.stringify(result.tokenInfo, null, 2));
    } else {
      console.log("❌ Connection failed:", result.error);
    }

    console.log("\n3️⃣ Testing token info endpoint...");
    const tokenInfo = authService.getTokenInfo();
    console.log("Token Info:", JSON.stringify(tokenInfo, null, 2));
  } catch (error) {
    console.error("❌ Test failed:", error.message);

    if (error.message.includes("GOOGLE_CLIENT_ID")) {
      console.log("\n🔧 Setup Required:");
      console.log("1. Tạo file .env với:");
      console.log("   GOOGLE_CLIENT_ID=your_client_id");
      console.log("   GOOGLE_CLIENT_SECRET=your_client_secret");
      console.log("2. Lấy credentials từ: https://console.cloud.google.com/");
    }
  }
}

testTokenManagement();
