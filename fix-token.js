const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

console.log("🔧 Fix Refresh Token Helper\n");

// Kiểm tra .env file
const envPath = path.join(__dirname, ".env");
if (!fs.existsSync(envPath)) {
  console.log("❌ File .env không tồn tại. Tạo file .env từ .env.example:");
  console.log("   cp .env.example .env");
  process.exit(1);
}

console.log("📋 Các bước để fix refresh token:\n");

console.log("1️⃣ MỞ BROWSER VÀ TRUY CẬP URL:");
console.log("   Copy URL bên dưới và paste vào browser:\n");

// Chạy get-refresh-token.js để lấy URL
try {
  const output = execSync("node get-refresh-token.js", { encoding: "utf8" });
  console.log(output);
} catch (error) {
  console.error("❌ Lỗi khi chạy get-refresh-token.js:", error.message);
  process.exit(1);
}

console.log("\n2️⃣ SAU KHI AUTHORIZE:");
console.log('   - Click "Allow" để authorize application');
console.log('   - Copy authorization code từ URL (phần sau "code=")');
console.log("   - Code sẽ có dạng: 4/0AX4XfWh...\n");

console.log("3️⃣ GENERATE REFRESH TOKEN:");
console.log("   Chạy lệnh sau với code vừa copy:");
console.log("   node get-refresh-token.js YOUR_AUTHORIZATION_CODE\n");

console.log("4️⃣ CẬP NHẬT .ENV:");
console.log("   - Copy refresh token từ output");
console.log("   - Paste vào file .env: GOOGLE_REFRESH_TOKEN=your_token_here\n");

console.log("5️⃣ RESTART SERVER:");
console.log("   node server.js\n");

console.log("💡 LƯU Ý:");
console.log("   - Refresh token chỉ cần lấy 1 lần duy nhất");
console.log("   - Token có thể hết hạn sau 7 ngày nếu app chưa được verify");
console.log("   - Đảm bảo enable Google Docs API & Drive API trong Console\n");

console.log("🆘 NẾU VẪN LỖI:");
console.log("   1. Kiểm tra GOOGLE_CLIENT_ID và GOOGLE_CLIENT_SECRET");
console.log("   2. Kiểm tra redirect URI trong Google Cloud Console");
console.log("   3. Revoke access: https://myaccount.google.com/permissions");
console.log("   4. Tạo lại credentials mới\n");
