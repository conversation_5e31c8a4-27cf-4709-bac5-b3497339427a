const http = require("http");
const TokenManager = require("./TokenManager");

console.log("🧪 Testing Port Conflict Resolution\n");

// Mock credentials for testing
const mockCredentials = {
  GOOGLE_CLIENT_ID: "test_client_id",
  GOOGLE_CLIENT_SECRET: "test_client_secret",
};

async function testPortFinding() {
  console.log("1️⃣ Testing basic port availability check...");

  const tokenManager = new TokenManager(mockCredentials);

  // Test port availability checking
  const isPort3001Available = await tokenManager.isPortAvailable(3001);
  console.log(`   Port 3001 available: ${isPort3001Available}`);

  const isPort80Available = await tokenManager.isPortAvailable(80); // Should be false (system port)
  console.log(`   Port 80 available: ${isPort80Available}`);

  console.log("\n2️⃣ Testing dynamic port finding...");

  try {
    const availablePort = await tokenManager.findAvailablePort(3001, 3010);
    console.log(`   ✅ Found available port: ${availablePort}`);
  } catch (error) {
    console.log(`   ❌ No available port found: ${error.message}`);
  }

  console.log("\n3️⃣ Testing port conflict simulation...");

  // Create a server to occupy port 3001
  const blockingServer = http.createServer();

  try {
    await new Promise((resolve, reject) => {
      blockingServer.listen(3001, () => {
        console.log("   📍 Blocking server started on port 3001");
        resolve();
      });
      blockingServer.on("error", reject);
    });

    // Now test if TokenManager can find alternative port
    const alternativePort = await tokenManager.findAvailablePort(3001, 3010);
    console.log(`   ✅ Found alternative port: ${alternativePort}`);

    if (alternativePort !== 3001) {
      console.log("   🎉 Port conflict resolution successful!");
    } else {
      console.log("   ❌ Expected different port than 3001");
    }
  } catch (error) {
    console.log(`   ❌ Port conflict test failed: ${error.message}`);
  } finally {
    blockingServer.close();
    console.log("   🧹 Cleanup: Blocking server closed");
  }

  console.log("\n4️⃣ Testing extreme conflict scenario...");

  // Create multiple servers to occupy many ports
  const blockingServers = [];
  const blockedPorts = [3001, 3002, 3003, 3004, 3005];

  try {
    // Start multiple blocking servers
    for (const port of blockedPorts) {
      const server = http.createServer();
      await new Promise((resolve, reject) => {
        server.listen(port, () => {
          console.log(`   📍 Blocking port ${port}`);
          resolve();
        });
        server.on("error", reject);
      });
      blockingServers.push(server);
    }

    // Test finding port beyond blocked range
    const freePort = await tokenManager.findAvailablePort(3001, 3010);
    console.log(`   ✅ Found free port despite conflicts: ${freePort}`);

    if (!blockedPorts.includes(freePort)) {
      console.log("   🎉 Advanced conflict resolution successful!");
    }
  } catch (error) {
    console.log(`   ❌ Extreme conflict test failed: ${error.message}`);
  } finally {
    // Cleanup all blocking servers
    blockingServers.forEach((server) => server.close());
    console.log("   🧹 Cleanup: All blocking servers closed");
  }

  console.log("\n✅ Port conflict testing completed!");
  console.log("\n📊 Summary:");
  console.log("  ✅ Dynamic port finding implemented");
  console.log("  ✅ Port conflict detection working");
  console.log("  ✅ Alternative port resolution working");
  console.log("  ✅ Multiple conflict handling working");
  console.log("  ✅ Server error handling implemented");
}

// Helper to simulate port occupation
async function occupyPortTemporarily(port, duration = 5000) {
  return new Promise((resolve) => {
    const server = http.createServer();
    server.listen(port, () => {
      console.log(`⏱️  Temporarily occupying port ${port} for ${duration}ms`);
      setTimeout(() => {
        server.close(() => {
          console.log(`🔓 Released port ${port}`);
          resolve();
        });
      }, duration);
    });
  });
}

if (require.main === module) {
  testPortFinding().catch(console.error);
}
