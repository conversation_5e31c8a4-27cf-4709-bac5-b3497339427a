"use strict";
require("dotenv").config();

const express = require("express");
const helmet = require("helmet");
const compression = require("compression");
const cors = require("cors");
const morgan = require("morgan");
const axios = require("axios");
const { google } = require("googleapis");
const { LRUCache } = require("lru-cache");
const rateLimit = require("express-rate-limit");
const sanitizeHtml = require("sanitize-html");
const GoogleAuthService = require("./GoogleAuthService");

// ---------- Environment checks và Google Auth Service ----------
const PORT = Number(process.env.PORT || 3000);
const CACHE_TTL_SECONDS = Number(process.env.CACHE_TTL_SECONDS || 120);

// Initialize Google Auth Service với tự động token management
let googleAuthService;
try {
  googleAuthService = new GoogleAuthService();
  console.log("🚀 Server starting with automatic token management...");
} catch (error) {
  console.error("❌ Failed to initialize Google Auth Service:", error.message);
  console.log(
    "📝 Hãy đảm bảo GOOGLE_CLIENT_ID và GOOGLE_CLIENT_SECRET được set trong .env"
  );
  process.exit(1);
}

// ---------- App setup ----------
const app = express();
app.disable("x-powered-by");
app.use(
  helmet({
    contentSecurityPolicy: {
      useDefaults: true,
      directives: {
        // Allow images from our own origin and our proxy route
        "img-src": ["'self'", "data:"],
        // Allow inline styles in the exported HTML (limited)
        "style-src": ["'self'", "'unsafe-inline'"],
      },
    },
  })
);
app.use(compression());

// CORS allowlist
const originsEnv = process.env.CORS_ORIGINS || "*";
const allowedOrigins =
  originsEnv === "*" ? "*" : originsEnv.split(",").map((s) => s.trim());
app.use(
  cors({
    origin:
      allowedOrigins === "*"
        ? true
        : function (origin, cb) {
            if (!origin || allowedOrigins.includes(origin))
              return cb(null, true);
            return cb(new Error("Not allowed by CORS"));
          },
  })
);

app.use(express.json({ limit: "1mb" }));
app.use(morgan("combined"));

// ---------- Caches ----------
// Cache HTML export keyed by fileId + modifiedTime
const htmlCache = new LRUCache({
  max: 100, // up to 100 docs
  ttl: CACHE_TTL_SECONDS * 1000,
});
// Cache metadata (modifiedTime) to support conditional GETs
const metaCache = new LRUCache({ max: 200, ttl: 60 * 1000 });

// ---------- Utilities ----------
function buildETag(modifiedTime) {
  // Use a weak ETag based on modifiedTime
  return `W/\"${modifiedTime}\"`;
}

function wrapHtmlPage(title, bodyHtml) {
  return `<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>${escapeHtml(title || "Document")}</title>
  <style>
    body { max-width: 900px; margin: 24px auto; padding: 0 16px; font-family: system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif; line-height: 1.6; }
    img { max-width: 100%; height: auto; }
    table { border-collapse: collapse; }
    table, th, td { border: 1px solid #ddd; }
    th, td { padding: 6px 10px; }
    a { text-decoration: none; }
    .doc-container { background: #fff; }
  </style>
</head>
<body>
  <div class="doc-container">${bodyHtml}</div>
</body>
</html>`;
}

function escapeHtml(str = "") {
  return str.replace(
    /[&<>"]/g,
    (c) => ({ "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;" }[c])
  );
}

// function sanitizeExportedHtml(rawHtml) {
//   return sanitizeHtml(rawHtml, {
//     allowedTags: sanitizeHtml.defaults.allowedTags.concat([
//       "img",
//       "h1",
//       "h2",
//       "h3",
//       "h4",
//       "h5",
//       "h6",
//       "table",
//       "thead",
//       "tbody",
//       "tfoot",
//       "tr",
//       "th",
//       "td",
//       "span",
//       "div",
//       "figure",
//       "figcaption",
//     ]),
//     allowedAttributes: {
//       "*": ["id", "class", "style"],
//       a: ["href", "name", "target", "rel"],
//       img: ["src", "alt", "title"],
//     },
//     transformTags: {
//       a: (tagName, attribs) => ({
//         tagName: "a",
//         attribs: { ...attribs, rel: "noopener nofollow", target: "_blank" },
//       }),
//     },
//     // Prevent loading remote scripts
//     allowedSchemesByTag: {
//       img: ["data"], // after rewrite we will keep images as relative (same-origin) or data URIs; deny http(s)
//     },

//     // Keep CSS but sanitize. Consider further hardening if you embed untrusted docs widely.
//     allowVulnerableTags: true,
//   });
// }

function sanitizeExportedHtml(rawHtml) {
  return sanitizeHtml(rawHtml, {
    allowedTags: sanitizeHtml.defaults.allowedTags.concat([
      "img",
      "h1",
      "h2",
      "h3",
      "h4",
      "h5",
      "h6",
      "table",
      "thead",
      "tbody",
      "tfoot",
      "tr",
      "th",
      "td",
      "span",
      "div",
      "figure",
      "figcaption",
    ]),
    allowedAttributes: {
      "*": ["id", "class", "style"],
      a: ["href", "name", "target", "rel"],
      img: ["src", "alt", "title"],
    },
    transformTags: {
      a: (tagName, attribs) => ({
        tagName: "a",
        attribs: { ...attribs, rel: "noopener nofollow", target: "_blank" },
      }),
    },
    allowedSchemesByTag: {
      img: ["http", "https", "data"],
    },
    allowVulnerableTags: true,
  });
}
function rewriteImageSrcToProxy(html, fileId) {
  // Replace external Google image URLs to our authenticated proxy endpoint
  const imgSrcRegex = /<img\b[^>]*?src=["']([^"']+)["'][^>]*>/gi;
  return html.replace(imgSrcRegex, (match, src) => {
    try {
      const u = new URL(src, "https://dummy.base"); // base for relative
      const host = u.host;
      const isGoogle =
        /googleusercontent\.com$/.test(host) || /google\.com$/.test(host);
      if (isGoogle) {
        const proxied =
          `/docs/${encodeURIComponent(fileId)}/asset?url=` +
          encodeURIComponent(src);
        return match.replace(src, proxied);
      }
      // Default: drop http(s) external images for CSP safety
      const proxied =
        `/docs/${encodeURIComponent(fileId)}/asset?url=` +
        encodeURIComponent(src);
      return match.replace(src, proxied);
    } catch (_) {
      return match; // leave as is
    }
  });
}

async function getAccessToken() {
  return await googleAuthService.getAccessToken();
}

async function fetchDocMeta(fileId) {
  const cached = metaCache.get(fileId);
  if (cached) return cached;

  const authClient = await googleAuthService.getAuthClient();
  const drive = google.drive({ version: "v3", auth: authClient });

  const { data } = await drive.files.get({
    fileId,
    fields: "id, name, mimeType, modifiedTime",
  });
  const meta = {
    id: data.id,
    name: data.name,
    mimeType: data.mimeType,
    modifiedTime: data.modifiedTime,
  };
  metaCache.set(fileId, meta);
  return meta;
}

async function exportDocAsHtml(fileId) {
  const meta = await fetchDocMeta(fileId);
  console.log(meta);
  const cacheKey = `${fileId}:${meta.modifiedTime}`;
  let cached = htmlCache.get(cacheKey);
  if (cached) return { ...cached, meta };

  // Export Google Doc to HTML (single file)
  const authClient = await googleAuthService.getAuthClient();
  const drive = google.drive({ version: "v3", auth: authClient });

  const res = await drive.files.export(
    { fileId, mimeType: "text/html" },
    { responseType: "arraybuffer" }
  );

  let html = Buffer.from(res.data).toString("utf8");
  html = rewriteImageSrcToProxy(html, fileId);
  html = sanitizeExportedHtml(html);
  // console.log(html);
  const payload = { html, etag: buildETag(meta.modifiedTime) };
  htmlCache.set(cacheKey, payload);
  return { ...payload, meta };
}

// ---------- Routes ----------
app.get("/health", (req, res) => res.status(200).send("ok"));

// Test connection và refresh token status (auto-authorization nếu cần)
app.get("/test-connection", async (req, res) => {
  try {
    // Test connection đến Google API (TokenManager sẽ tự động handle authorization nếu cần)
    const connectionTest = await googleAuthService.testConnection();

    res.json({
      ...connectionTest,
      server: "gdocs-html-server",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Test connection failed:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Test connection failed",
      server: "gdocs-html-server",
      timestamp: new Date().toISOString(),
    });
  }
});

// Lấy thông tin về token hiện tại
app.get("/token-info", (req, res) => {
  try {
    const tokenInfo = googleAuthService.getTokenInfo();
    res.json({
      success: true,
      tokenInfo,
      message: "Token info retrieved",
      server: "gdocs-html-server",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Get token info failed:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to get token info",
      server: "gdocs-html-server",
      timestamp: new Date().toISOString(),
    });
  }
});

// Reset tokens (xóa token.json và force re-authorization)
app.post("/reset-tokens", async (req, res) => {
  try {
    // Access TokenManager qua GoogleAuthService
    await googleAuthService.tokenManager.removeTokenFile();

    res.json({
      success: true,
      message:
        "Tokens reset successfully. Next request will trigger re-authorization.",
      server: "gdocs-html-server",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Reset tokens failed:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to reset tokens",
      server: "gdocs-html-server",
      timestamp: new Date().toISOString(),
    });
  }
});

// Debug endpoint để kiểm tra token file status
app.get("/debug/token-status", async (req, res) => {
  try {
    const fs = require("fs").promises;
    const path = require("path");
    const tokenPath = path.join(__dirname, "token.json");

    let tokenFileInfo = null;
    try {
      const tokenData = await fs.readFile(tokenPath, "utf8");
      tokenFileInfo = {
        exists: true,
        size: tokenData.length,
        content: tokenData.trim() ? "Valid content" : "Empty file",
        isValidJSON: false,
      };

      try {
        const parsed = JSON.parse(tokenData);
        tokenFileInfo.isValidJSON = true;
        tokenFileInfo.hasAccessToken = !!parsed.access_token;
        tokenFileInfo.hasRefreshToken = !!parsed.refresh_token;
        tokenFileInfo.expiryDate = parsed.expiry_date;
      } catch (parseError) {
        tokenFileInfo.parseError = parseError.message;
      }
    } catch (fileError) {
      tokenFileInfo = {
        exists: false,
        error: fileError.message,
      };
    }

    const tokenInfo = googleAuthService.getTokenInfo();

    res.json({
      success: true,
      tokenFile: tokenFileInfo,
      tokenManager: tokenInfo,
      server: "gdocs-html-server",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Debug token status failed:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to get token status",
      server: "gdocs-html-server",
      timestamp: new Date().toISOString(),
    });
  }
});

// GET /docs/:fileId — returns a full HTML page (template + doc). Use ?raw=1 to return ONLY the sanitized body
app.get("/docs/:fileId", async (req, res) => {
  try {
    const fileId = req.params.fileId;
    console.log(fileId);
    const { html, etag, meta } = await exportDocAsHtml(fileId);

    // Conditional GET with ETag
    if (req.headers["if-none-match"] === etag) {
      return res.status(304).end();
    }

    res.setHeader("ETag", etag);
    res.setHeader("Cache-Control", `public, max-age=${CACHE_TTL_SECONDS}`);
    console.log(html);
    const isRaw = String(req.query.raw || "0") === "1";
    console.log(isRaw);
    if (isRaw) {
      res.type("html").status(200).send(html);
    } else {
      const page = wrapHtmlPage(meta.name, html);
      res.type("html").status(200).send(page);
    }
  } catch (err) {
    console.error("Error /docs/:fileId", err?.response?.data || err);
    const status = err?.response?.status || 500;
    res.status(status).json({
      error: "Failed to export Google Doc",
      detail: err?.message || "Unknown error",
    });
  }
});

// Proxy images and other assets referenced by the exported HTML
// GET /docs/:fileId/asset?url=<encoded>
app.get("/docs/:fileId/asset", async (req, res) => {
  try {
    const rawUrl = req.query.url;
    if (!rawUrl)
      return res.status(400).json({ error: "Missing url parameter" });

    const url = new URL(String(rawUrl));
    // Simple allowlist — tighten as needed
    const allowedHosts = [
      "lh3.googleusercontent.com",
      "docs.googleusercontent.com",
      "drive.google.com",
      "googleusercontent.com",
    ];

    if (!allowedHosts.some((h) => url.host.endsWith(h))) {
      return res.status(400).json({ error: "Blocked host" });
    }

    const token = await getAccessToken();

    const upstream = await axios.get(url.toString(), {
      responseType: "stream",
      headers: { Authorization: `Bearer ${token}` },
    });

    // Forward critical headers
    if (upstream.headers["content-type"])
      res.setHeader("Content-Type", upstream.headers["content-type"]);
    if (upstream.headers["content-length"])
      res.setHeader("Content-Length", upstream.headers["content-length"]);
    res.setHeader("Cache-Control", "public, max-age=86400");

    upstream.data.pipe(res);
  } catch (err) {
    console.error("Error proxying asset", err?.response?.status, err?.message);
    res.status(502).json({ error: "Failed to fetch asset" });
  }
});

// Basic rate limiting (adjust to your traffic)
const limiter = rateLimit({ windowMs: 60 * 1000, max: 120 });
app.use(limiter);

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: "Not Found" });
});

// Error handler
app.use((err, req, res, next) => {
  console.error("Unhandled error:", err);
  res.status(500).json({ error: "Internal Server Error" });
});

app.listen(PORT, () => {
  console.log(`gdocs-html-server listening on http://localhost:${PORT}`);
});
